# Docker Setup for GetLead Laravel Application

This Docker setup provides a complete development and production environment for the GetLead Laravel application with React frontend, including MySQL 8.0 and Redis.

## Services Included

- **App Container**: Laravel application with PHP 8.2-FPM, Nginx, and Node.js
- **MySQL 8.0**: Database server with optimized configuration
- **Redis**: Caching and session storage
- **phpMyAdmin**: Database management interface (optional)

## Quick Start

### 1. Environment Setup

Copy the Docker environment file:
```bash
cp .env.docker .env
```

Generate an application key:
```bash
php artisan key:generate
```

### 2. Build and Start Services

Build and start all services:
```bash
docker-compose up -d --build
```

### 3. Application Setup

Run database migrations:
```bash
docker-compose exec app php artisan migrate
```

Seed the database (if you have seeders):
```bash
docker-compose exec app php artisan db:seed
```

Clear and cache configuration:
```bash
docker-compose exec app php artisan config:cache
docker-compose exec app php artisan route:cache
docker-compose exec app php artisan view:cache
```

## Access Points

- **Application**: http://localhost:8000
- **phpMyAdmin**: http://localhost:8080
- **MySQL**: localhost:3306
- **Redis**: localhost:6379

## Database Credentials

- **Root Password**: `root_password`
- **Database**: `getlead`
- **Username**: `getlead_user`
- **Password**: `secure_password`

## Useful Commands

### Application Commands
```bash
# Access application container
docker-compose exec app sh

# Run Artisan commands
docker-compose exec app php artisan migrate
docker-compose exec app php artisan tinker

# View logs
docker-compose logs app
docker-compose logs mysql
docker-compose logs redis

# Restart services
docker-compose restart app
```

### Development Commands
```bash
# Install new PHP dependencies
docker-compose exec app composer install

# Install new Node.js dependencies
docker-compose exec app npm install

# Build frontend assets
docker-compose exec app npm run build

# Run tests
docker-compose exec app php artisan test
```

### Database Commands
```bash
# Access MySQL CLI
docker-compose exec mysql mysql -u root -p

# Backup database
docker-compose exec mysql mysqldump -u root -p getlead > backup.sql

# Restore database
docker-compose exec -T mysql mysql -u root -p getlead < backup.sql
```

### Redis Commands
```bash
# Access Redis CLI
docker-compose exec redis redis-cli

# Monitor Redis
docker-compose exec redis redis-cli monitor

# Clear Redis cache
docker-compose exec redis redis-cli FLUSHALL
```

## File Structure

```
docker/
├── nginx.conf          # Nginx configuration
├── php.ini            # PHP configuration
├── supervisord.conf   # Supervisor configuration
├── mysql/
│   └── my.cnf        # MySQL configuration
└── redis/
    └── redis.conf    # Redis configuration
```

## Production Considerations

1. **Security**: Change default passwords in production
2. **SSL**: Configure SSL certificates for HTTPS
3. **Environment**: Update `.env` file with production values
4. **Volumes**: Consider using named volumes for data persistence
5. **Monitoring**: Add monitoring and logging solutions
6. **Backup**: Implement regular database backups

## Troubleshooting

### Common Issues

1. **Permission Issues**:
   ```bash
   docker-compose exec app chown -R www-data:www-data /var/www/html/storage
   docker-compose exec app chmod -R 755 /var/www/html/storage
   ```

2. **Database Connection Issues**:
   - Ensure MySQL container is healthy: `docker-compose ps`
   - Check database credentials in `.env` file

3. **Redis Connection Issues**:
   - Verify Redis container is running: `docker-compose logs redis`
   - Check Redis configuration

4. **Frontend Assets Not Loading**:
   ```bash
   docker-compose exec app npm run build
   ```

### Logs and Debugging

View container logs:
```bash
docker-compose logs -f app
docker-compose logs -f mysql
docker-compose logs -f redis
```

## Stopping Services

Stop all services:
```bash
docker-compose down
```

Stop and remove volumes (⚠️ This will delete all data):
```bash
docker-compose down -v
```
