{"name": "laravel/react-starter-kit", "description": "The skeleton application for the Laravel framework.", "license": "MIT", "type": "project", "keywords": ["laravel", "framework"], "require": {"php": "^8.2", "inertiajs/inertia-laravel": "^2.0.4", "laravel/framework": "^12.21.0", "laravel/tinker": "^2.10.1", "tightenco/ziggy": "^2.5.3"}, "require-dev": {"ergebnis/composer-normalize": "^2.47.0", "fakerphp/faker": "^1.24.1", "laravel/pail": "^1.2.3", "laravel/pint": "^1.24.0", "laravel/sail": "^1.44.0", "mockery/mockery": "^1.6.12", "nunomaduro/collision": "^8.8.2", "pestphp/pest": "^3.8.2", "pestphp/pest-plugin-laravel": "^3.2.0"}, "minimum-stability": "stable", "prefer-stable": true, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "config": {"allow-plugins": {"ergebnis/composer-normalize": true, "pestphp/pest-plugin": true, "php-http/discovery": true}, "optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "extra": {"laravel": {"dont-discover": []}}, "scripts": {"post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite --kill-others"], "dev:ssr": ["npm run build:ssr", "Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"php artisan inertia:start-ssr\" --names=server,queue,logs,ssr --kill-others"], "test": ["@php artisan config:clear --ansi", "@php artisan test"]}, "$schema": "https://getcomposer.org/schema.json"}